<?php

// Iniciar sesión siempre al principio
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}
/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

use App\classes\Proyecto;

// Initialize response array for AJAX requests
$response = [
    'success' => false,
    'message' => 'Error desconocido.'
];

// Get all proyectos for display
try {
    $proyectos = Proyecto::obtenerTodos($conexion);
} catch (Exception $e) {
    error_log("Error loading proyectos: " . $e->getMessage());
    $proyectos = [];
}

// Handle AJAX POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Set content type for JSON responses
    header('Content-Type: application/json');
    
    try {
        $action = limpiar_datos($_POST['action'] ?? '');
        
        switch ($action) {
            case 'crear':
                handleCrearProyecto($conexion, $response);
                break;
            
            case 'editar':
                handleEditarProyecto($conexion, $response);
                break;
            
            case 'eliminar':
                handleEliminarProyecto($conexion, $response);
                break;
            
            case 'obtener':
                handleObtenerProyecto($conexion, $response);
                break;
            
            default:
                $response['message'] = 'Acción no válida.';
                break;
        }
        
    } catch (Exception $e) {
        error_log("Error en lproyectos.php: " . $e->getMessage());
        $response['message'] = 'Error interno del servidor. Por favor, intente de nuevo.';
    }
    
    // Return JSON response for AJAX requests
    echo json_encode($response);
    exit();
}

/**
 * Handle creating a new proyecto
 */
function handleCrearProyecto(PDO $conexion, array &$response): void
{
    try {
        // Validate and clean input data
        $titulo = limpiar_datos($_POST['titulo'] ?? '');
        $ubicacion = limpiar_datos($_POST['ubicacion'] ?? '');
        $descripcion = limpiar_datos($_POST['descripcion'] ?? '');
        $prioridad = isset($_POST['prioridad']) ? (int)$_POST['prioridad'] : null;
        
        // Handle portada upload
        $imagen_portada = null;
        if (isset($_FILES['portada']) && $_FILES['portada']['error'] === UPLOAD_ERR_OK) {
            $imagen_portada = handlePortadaUpload($_FILES['portada']);
        }
        
        // Handle logo_cliente upload
        $logo_cliente = null;
        if (isset($_FILES['logo_cliente']) && $_FILES['logo_cliente']['error'] === UPLOAD_ERR_OK) {
            $logo_cliente = handleLogoUpload($_FILES['logo_cliente']);
        }
        
        // Server-side validation
        serverSideValidation($titulo, $ubicacion, $descripcion, $prioridad, $imagen_portada, $logo_cliente);
        
        // Create new proyecto object
        $proyecto = new Proyecto();
        $proyecto->setTitulo($titulo)
            ->setUbicacion($ubicacion)
            ->setDescripcion($descripcion)
            ->setPrioridad($prioridad)
            ->setImagenPortada($imagen_portada)
            ->setLogoCliente($logo_cliente);
        
        // Save to database
        $proyectoId = $proyecto->crear($conexion);
        
        if ($proyectoId) {
            $response['success'] = true;
            $response['message'] = 'Proyecto creado exitosamente.';
        } else {
            throw new Exception('No se pudo crear el proyecto.');
        }
        
    } catch (Exception $e) {
        $response['message'] = $e->getMessage();
    }
}

/**
 * Handle editing an existing proyecto
 */
function handleEditarProyecto(PDO $conexion, array &$response): void
{
    try {
        // Get and validate ID
        $id = isset($_POST['id']) ? (int)$_POST['id'] : null;
        if (!$id) {
            throw new Exception('ID del proyecto es requerido.');
        }
        
        // Get current proyecto
        $currentProyecto = Proyecto::obtenerPorId($id, $conexion);
        if (!$currentProyecto) {
            throw new Exception('Proyecto no encontrado.');
        }
        
        // Validate and clean input data
        $titulo = limpiar_datos($_POST['titulo'] ?? '');
        $ubicacion = limpiar_datos($_POST['ubicacion'] ?? '');
        $descripcion = limpiar_datos($_POST['descripcion'] ?? '');
        $prioridad = isset($_POST['prioridad']) ? (int)$_POST['prioridad'] : null;
        
        // Handle portada upload (optional for edit)
        $imagen_portada = $currentProyecto->getImagenPortada(); // Keep current image by default
        if (isset($_FILES['portada']) && $_FILES['portada']['error'] === UPLOAD_ERR_OK) {
            $imagen_portada = handlePortadaUpload($_FILES['portada']);
            // TODO: Delete old image file if needed
        }
        
        // Handle logo_cliente upload (optional for edit)
        $logo_cliente = $currentProyecto->getLogoCliente(); // Keep current logo by default
        if (isset($_FILES['logo_cliente']) && $_FILES['logo_cliente']['error'] === UPLOAD_ERR_OK) {
            $logo_cliente = handleLogoUpload($_FILES['logo_cliente']);
            // TODO: Delete old logo file if needed
        }
        
        // Server-side validation
        serverSideValidation($titulo, $ubicacion, $descripcion, $prioridad, null, null, $id); // Don't require images for edit
        
        // Update proyecto
        $success = Proyecto::modificarCompleto($id, $imagen_portada, $logo_cliente, $titulo, $ubicacion, $descripcion, $prioridad, $conexion);
        
        if ($success) {
            $response['success'] = true;
            $response['message'] = 'Proyecto actualizado exitosamente.';
        } else {
            throw new Exception('No se pudo actualizar el proyecto.');
        }
        
    } catch (Exception $e) {
        $response['message'] = $e->getMessage();
    }
}

/**
 * Handle deleting a proyecto (soft delete)
 */
function handleEliminarProyecto(PDO $conexion, array &$response): void
{
    try {
        // Get and validate ID
        $id = isset($_POST['id']) ? (int)$_POST['id'] : null;
        if (!$id) {
            throw new Exception('ID del proyecto es requerido.');
        }
        
        // Soft delete the proyecto
        $success = Proyecto::eliminar($id, $conexion);
        
        if ($success) {
            $response['success'] = true;
            $response['message'] = 'Proyecto eliminado exitosamente.';
        } else {
            throw new Exception('No se pudo eliminar el proyecto.');
        }
        
    } catch (Exception $e) {
        $response['message'] = $e->getMessage();
    }
}

/**
 * Handle getting a proyecto for editing
 */
function handleObtenerProyecto(PDO $conexion, array &$response): void
{
    try {
        // Get and validate ID
        $id = isset($_POST['id']) ? (int)$_POST['id'] : null;
        if (!$id) {
            throw new Exception('ID del proyecto es requerido.');
        }
        
        // Get proyecto
        $proyecto = Proyecto::obtenerPorId($id, $conexion);
        
        if ($proyecto) {
            $response['success'] = true;
            $response['proyecto'] = [
                'id' => $proyecto->getId(),
                'titulo' => $proyecto->getTitulo(),
                'ubicacion' => $proyecto->getUbicacion(),
                'descripcion' => $proyecto->getDescripcion(),
                'prioridad' => $proyecto->getPrioridad(),
                'imagen_portada' => $proyecto->getImagenPortada(),
                'logo_cliente' => $proyecto->getLogoCliente()
            ];
        } else {
            throw new Exception('Proyecto no encontrado.');
        }
        
    } catch (Exception $e) {
        $response['message'] = $e->getMessage();
    }
}

/**
 * Handle portada image upload
 */
function handlePortadaUpload(array $file): string
{
    $uploadDir = __ROOT__ . '/resources/images/proyectos/';
    $allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    $maxSize = 2 * 1024 * 1024; // 2MB
    
    // Validate file type
    if (!in_array($file['type'], $allowedTypes)) {
        throw new Exception('Tipo de archivo no válido para portada. Solo se permiten JPG, PNG y WebP.');
    }
    
    // Validate file size
    if ($file['size'] > $maxSize) {
        throw new Exception('El archivo de portada es muy grande. Máximo 2MB.');
    }
    
    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'portada_' . uniqid() . '.' . $extension;
    $filepath = $uploadDir . $filename;
    
    // Create directory if it doesn't exist
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        throw new Exception('Error al subir la imagen de portada.');
    }
    
    return $filename;
}

/**
 * Handle logo cliente upload
 */
function handleLogoUpload(array $file): string
{
    $uploadDir = __ROOT__ . '/resources/images/proyectos/logos/';
    $allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    $maxSize = 600 * 1024; // 600KB
    
    // Validate file type
    if (!in_array($file['type'], $allowedTypes)) {
        throw new Exception('Tipo de archivo no válido para logo. Solo se permiten JPG, PNG y WebP.');
    }
    
    // Validate file size
    if ($file['size'] > $maxSize) {
        throw new Exception('El archivo de logo es muy grande. Máximo 600KB.');
    }
    
    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'logo_' . uniqid() . '.' . $extension;
    $filepath = $uploadDir . $filename;
    
    // Create directory if it doesn't exist
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        throw new Exception('Error al subir el logo del cliente.');
    }
    
    return $filename;
}

/**
 * Server-side validation
 */
function serverSideValidation(string $titulo, string $ubicacion, string $descripcion, ?int $prioridad, ?string $imagen_portada, ?string $logo_cliente, ?int $excludeId = null): void
{
    // Validate title
    if (empty(trim($titulo))) {
        throw new Exception('El título es requerido.');
    }

    if (strlen(trim($titulo)) < 2) {
        throw new Exception('El título debe tener al menos 2 caracteres.');
    }

    if (strlen(trim($titulo)) > 200) {
        throw new Exception('El título no puede exceder 200 caracteres.');
    }

    // Validate ubicacion
    if (empty(trim($ubicacion))) {
        throw new Exception('La ubicación es requerida.');
    }

    if (strlen(trim($ubicacion)) < 2) {
        throw new Exception('La ubicación debe tener al menos 2 caracteres.');
    }

    if (strlen(trim($ubicacion)) > 200) {
        throw new Exception('La ubicación no puede exceder 200 caracteres.');
    }

    // Validate description
    if (empty(trim($descripcion))) {
        throw new Exception('La descripción es requerida.');
    }

    if (strlen(trim($descripcion)) < 10) {
        throw new Exception('La descripción debe tener al menos 10 caracteres.');
    }

    if (strlen(trim($descripcion)) > 2000) {
        throw new Exception('La descripción no puede exceder 2000 caracteres.');
    }

    // Validate priority
    if ($prioridad === null || $prioridad < 1) {
        throw new Exception('La prioridad es requerida y debe ser mayor a 0.');
    }

    if ($prioridad > 999) {
        throw new Exception('La prioridad no puede ser mayor a 999.');
    }

    // Validate images are required for creation
    if ($excludeId === null) {
        if (empty($imagen_portada)) {
            throw new Exception('La imagen de portada es requerida.');
        }
        if (empty($logo_cliente)) {
            throw new Exception('El logo del cliente es requerido.');
        }
    }
}

require_once __ROOT__ . '/views/admin/lproyectos.view.php';

?>
