<?php

// Iniciar sesión siempre al principio
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

use App\classes\Pilar;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

require_once __ROOT__ . '/vendor/autoload.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en apilares.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

// Initialize variables for form and feedback
$pilarData = null;
$success_text = '';
$success_display = 'hide';
$error_text = '';
$error_display = 'hide';

#region Handle Flash Messages
// Check for success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text = $_SESSION['flash_message_success'];
	$success_display = 'show';
	unset($_SESSION['flash_message_success']); // Clear after preparing display
}

// Check for error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text = $_SESSION['flash_message_error'];
	$error_display = 'show';
	unset($_SESSION['flash_message_error']); // Clear after preparing display
}
#endregion Handle Flash Messages

#region Load Existing Pilar Data
try {
	// Load existing pilar data to pre-populate the form
	$pilarData = Pilar::getPrincipal($conexion);
} catch (Exception $e) {
	error_log("Error loading pilar data: " . $e->getMessage());
	// Continue without pre-populated data - form will be empty
}
#endregion Load Existing Pilar Data

#region POST Request Handling
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	// Reset feedback vars for this request attempt
	$success_text = '';
	$success_display = 'hide';
	$error_text = '';
	$error_display = 'hide';

	try {
		// 1. Get data from $_POST and clean it
		$texto = !empty($_POST['texto']) ? limpiar_datos($_POST['texto']) : null;
		$pilar_titulo_1 = !empty($_POST['pilar_titulo_1']) ? limpiar_datos($_POST['pilar_titulo_1']) : null;
		$pilar_texto_1 = !empty($_POST['pilar_texto_1']) ? limpiar_datos($_POST['pilar_texto_1']) : null;
		$pilar_titulo_2 = !empty($_POST['pilar_titulo_2']) ? limpiar_datos($_POST['pilar_titulo_2']) : null;
		$pilar_texto_2 = !empty($_POST['pilar_texto_2']) ? limpiar_datos($_POST['pilar_texto_2']) : null;
		$pilar_titulo_3 = !empty($_POST['pilar_titulo_3']) ? limpiar_datos($_POST['pilar_titulo_3']) : null;
		$pilar_texto_3 = !empty($_POST['pilar_texto_3']) ? limpiar_datos($_POST['pilar_texto_3']) : null;

		// 2. Validate required fields
		if (empty($texto)) {
			throw new Exception("El texto principal es requerido.");
		}

		// 3. Handle image uploads
		$pilar_imagen_1 = null;
		$pilar_imagen_2 = null;
		$pilar_imagen_3 = null;

		// Process image uploads for each pilar
		for ($i = 1; $i <= 3; $i++) {
			$field_name = "pilar_imagen_$i";
			if (isset($_FILES[$field_name]) && $_FILES[$field_name]['error'] === UPLOAD_ERR_OK) {
				$uploaded_file = $_FILES[$field_name];
				
				// Validate file type
				$allowed_types = ['image/jpeg', 'image/jpg', 'image/png'];
				if (!in_array($uploaded_file['type'], $allowed_types)) {
					throw new Exception("Solo se permiten archivos JPG y PNG para la imagen del pilar $i.");
				}
				
				// Validate file size (600KB = 614400 bytes)
				if ($uploaded_file['size'] > 614400) {
					throw new Exception("La imagen del pilar $i no puede exceder los 600KB.");
				}
				
				// Create upload directory if it doesn't exist
				$upload_dir = __ROOT__ . '/resources/images/pilares/';
				if (!is_dir($upload_dir)) {
					mkdir($upload_dir, 0755, true);
				}
				
				// Generate unique filename
				$file_extension = pathinfo($uploaded_file['name'], PATHINFO_EXTENSION);
				$new_filename = 'pilar_' . $i . '_' . time() . '_' . uniqid() . '.' . $file_extension;
				$upload_path = $upload_dir . $new_filename;
				
				// Move uploaded file
				if (move_uploaded_file($uploaded_file['tmp_name'], $upload_path)) {
					${"pilar_imagen_$i"} = $new_filename;
					
					// Delete old image if exists
					if ($pilarData && $pilarData->{"getPilarImagen$i"}()) {
						$old_image_path = $upload_dir . $pilarData->{"getPilarImagen$i"}();
						if (file_exists($old_image_path)) {
							unlink($old_image_path);
						}
					}
				} else {
					throw new Exception("Error al subir la imagen del pilar $i.");
				}
			} else {
				// Keep existing image if no new image uploaded
				if ($pilarData) {
					${"pilar_imagen_$i"} = $pilarData->{"getPilarImagen$i"}();
				}
			}
		}

		// 4. Check if pilar exists and update or create accordingly
		$existingPilar = Pilar::getPrincipal($conexion);

		if ($existingPilar) {
			// Update existing pilar
			$success = Pilar::modificar(
				$existingPilar->getId(),
				$texto,
				$pilar_imagen_1,
				$pilar_titulo_1,
				$pilar_texto_1,
				$pilar_imagen_2,
				$pilar_titulo_2,
				$pilar_texto_2,
				$pilar_imagen_3,
				$pilar_titulo_3,
				$pilar_texto_3,
				$conexion
			);

			if ($success) {
				$_SESSION['flash_message_success'] = "Pilares actualizados correctamente.";
				header('Location: admin-pilares');
				exit;
			} else {
				throw new Exception("No se pudo actualizar los pilares.");
			}
		} else {
			// Create new pilar
			$newPilar = new Pilar();
			$newPilar->setTexto($texto)
					 ->setPilarImagen1($pilar_imagen_1)
					 ->setPilarTitulo1($pilar_titulo_1)
					 ->setPilarTexto1($pilar_texto_1)
					 ->setPilarImagen2($pilar_imagen_2)
					 ->setPilarTitulo2($pilar_titulo_2)
					 ->setPilarTexto2($pilar_texto_2)
					 ->setPilarImagen3($pilar_imagen_3)
					 ->setPilarTitulo3($pilar_titulo_3)
					 ->setPilarTexto3($pilar_texto_3);

			$newId = $newPilar->crear($conexion);

			if ($newId) {
				$_SESSION['flash_message_success'] = "Pilares creados correctamente.";
				header('Location: admin-pilares');
				exit;
			} else {
				throw new Exception("No se pudo crear los pilares.");
			}
		}

	} catch (PDOException $e) {
		// Handle database errors
		error_log("Database error in pilares configuration: " . $e->getMessage());
		$error_text = 'Error de base de datos al guardar los pilares. Por favor, intente de nuevo más tarde.';
		$error_display = 'show';
	} catch (Exception $e) {
		// Handle validation errors or other errors
		error_log("Error in pilares configuration: " . $e->getMessage());
		$error_text = 'Error: ' . htmlspecialchars($e->getMessage());
		$error_display = 'show';
	}
}
#endregion POST Request Handling

require_once __ROOT__ . '/views/admin/apilares.view.php';

?>
