<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

// Import PDOException for better error handling

class ProyectoImagen
{
	// --- Atributos ---
	private ?int    $id          = null;
	private ?int    $id_proyecto = null;
	private ?string $imagen      = null;
	private ?int    $prioridad   = null;
	
	/**
	 * Constructor: Inicializa las propiedades del objeto ProyectoImagen.
	 */
	public function __construct()
	{
		$this->id          = 0; // O null si prefieres no usar 0 por defecto
		$this->id_proyecto = null;
		$this->imagen      = null;
		$this->prioridad   = 0;
	}
	
	/**
	 * Método estático para construir un objeto ProyectoImagen desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos de la imagen del proyecto.
	 *
	 * @return self Instancia de ProyectoImagen.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto              = new self();
			$objeto->id          = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->id_proyecto = isset($resultado['id_proyecto']) ? (int)$resultado['id_proyecto'] : null;
			$objeto->imagen      = $resultado['imagen'] ?? null;
			$objeto->prioridad   = isset($resultado['prioridad']) ? (int)$resultado['prioridad'] : 0;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir ProyectoImagen: " . $e->getMessage());
		}
	}
	
	// --- Métodos de Acceso a Datos (Estáticos) ---
	
	/**
	 * Obtiene una imagen de proyecto por su ID.
	 *
	 * @param int $id       ID de la imagen del proyecto.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto ProyectoImagen o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener imagen de proyecto por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	pi.*
            FROM proyectos_imagenes pi
            WHERE
            	pi.id = :id
            LIMIT 1
            SQL;
			// Asume tabla 'proyectos_imagenes'
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);
			
			return $resultado ? self::construct($resultado) : null;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener ProyectoImagen (ID: $id): " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene una lista de imágenes de un proyecto específico.
	 *
	 * @param int $id_proyecto ID del proyecto.
	 * @param PDO $conexion    Conexión PDO.
	 *
	 * @return array Array de objetos ProyectoImagen.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list_by_proyecto(int $id_proyecto, PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de imágenes por proyecto (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	pi.*
            FROM proyectos_imagenes pi
            WHERE
            	pi.id_proyecto = :id_proyecto
            ORDER BY
            	pi.prioridad ASC, pi.id ASC
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_proyecto', $id_proyecto, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);
			
			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de ProyectoImagen por proyecto: " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene una lista de todas las imágenes de proyectos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos ProyectoImagen.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de todas las imágenes (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	pi.*
            FROM proyectos_imagenes pi
            ORDER BY
            	pi.id_proyecto ASC, pi.prioridad ASC, pi.id ASC
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);
			
			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de ProyectoImagen: " . $e->getMessage());
		}
	}

	/**
	 * Alias for get_list_by_proyecto() method for consistency with admin patterns.
	 *
	 * @param int $id_proyecto ID del proyecto.
	 * @param PDO $conexion    Conexión PDO.
	 *
	 * @return array Array de objetos ProyectoImagen.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorProyecto(int $id_proyecto, PDO $conexion): array
	{
		return self::get_list_by_proyecto($id_proyecto, $conexion);
	}

	/**
	 * Crea una nueva imagen de proyecto en la base de datos a partir de un objeto ProyectoImagen.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID de la nueva imagen creada o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getIdProyecto()) || empty($this->getImagen())) {
			throw new Exception("ID del proyecto e imagen son requeridos en el objeto ProyectoImagen para crearlo.");
		}

		// Validaciones de negocio
		$this->validarReglasNegocio($conexion);

		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO proyectos_imagenes (
            	 id_proyecto
            	,imagen
            	,prioridad
            ) VALUES (
            	 :id_proyecto
            	,:imagen
            	,:prioridad
            )
            SQL;
			// Asume tabla 'proyectos_imagenes'

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':id_proyecto', $this->getIdProyecto(), PDO::PARAM_INT);
			$statement->bindValue(':imagen', $this->getImagen(), PDO::PARAM_STR);
			$statement->bindValue(':prioridad', $this->getPrioridad() ?? 0, PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID de la imagen recién creada
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al crear imagen de proyecto: " . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear imagen de proyecto: " . $e->getMessage());
		}
	}
	
	/**
	 * Modifica una imagen de proyecto existente.
	 *
	 * @param int    $id          ID de la imagen a modificar.
	 * @param string $nuevaImagen La nueva imagen para el proyecto.
	 * @param PDO    $conexion    Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si la nueva imagen está vacía o si ocurre un error de base de datos.
	 */
	public static function modificar(int $id, string $nuevaImagen, PDO $conexion): bool
	{
		if (empty(trim($nuevaImagen))) {
			throw new Exception("La imagen no puede estar vacía.");
		}

		try {
			// Consulta para actualizar la imagen
			$query = <<<SQL
            UPDATE proyectos_imagenes SET
                imagen = :imagen
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':imagen', trim($nuevaImagen), PDO::PARAM_STR);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			$result = $statement->execute();

			// Verificar si se actualizó alguna fila
			if ($result && $statement->rowCount() === 0) {
				throw new Exception("No se encontró la imagen o no se realizaron cambios.");
			}

			return $result;

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al modificar imagen de proyecto (ID: $id): " . $e->getMessage());
		}
	}
	
	
	/**
	 * Elimina una imagen de proyecto (eliminación física).
	 *
	 * @param int $id       ID de la imagen a eliminar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la eliminación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function eliminar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para eliminar físicamente la imagen
			$query = <<<SQL
            DELETE FROM proyectos_imagenes
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			// Podríamos chequear rowCount() > 0 si queremos asegurarnos que una fila fue afectada.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			// Considera loggear el error aquí
			throw new Exception("Error de base de datos al eliminar imagen de proyecto (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Elimina todas las imágenes de un proyecto específico.
	 *
	 * @param int $id_proyecto ID del proyecto.
	 * @param PDO $conexion    Conexión PDO.
	 *
	 * @return bool True si la eliminación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function eliminar_por_proyecto(int $id_proyecto, PDO $conexion): bool
	{
		try {
			// Consulta para eliminar todas las imágenes de un proyecto
			$query = <<<SQL
            DELETE FROM proyectos_imagenes
            WHERE
            	id_proyecto = :id_proyecto
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_proyecto', $id_proyecto, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al eliminar imágenes del proyecto (ID: $id_proyecto): " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getIdProyecto(): ?int
	{
		return $this->id_proyecto;
	}

	public function setIdProyecto(?int $id_proyecto): self
	{
		$this->id_proyecto = $id_proyecto;
		return $this;
	}

	public function getImagen(): ?string
	{
		return $this->imagen;
	}

	public function setImagen(?string $imagen): self
	{
		$this->imagen = $imagen;
		return $this;
	}

	public function getPrioridad(): ?int
	{
		return $this->prioridad;
	}

	public function setPrioridad(?int $prioridad): self
	{
		$this->prioridad = $prioridad;
		return $this;
	}

	// --- Métodos adicionales ---

	/**
	 * Obtiene el objeto Proyecto asociado a esta imagen.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return Proyecto|null Objeto Proyecto o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public function getProyecto(PDO $conexion): ?Proyecto
	{
		if ($this->id_proyecto === null) {
			return null;
		}

		return Proyecto::get($this->id_proyecto, $conexion);
	}

	/**
	 * Valida las reglas de negocio para la creación de imágenes de proyecto.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @throws Exception Si las validaciones fallan.
	 */
	private function validarReglasNegocio(PDO $conexion): void
	{
		// Validar que el proyecto existe
		if (!empty($this->getIdProyecto())) {
			$query = "SELECT COUNT(*) FROM proyectos WHERE id = :id_proyecto AND estado = 1";
			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_proyecto', $this->getIdProyecto(), PDO::PARAM_INT);
			$statement->execute();

			if ($statement->fetchColumn() == 0) {
				throw new Exception("El proyecto especificado no existe o está inactivo.");
			}
		}

		// Validar que la imagen no esté vacía
		if (empty(trim($this->getImagen()))) {
			throw new Exception("La imagen es requerida.");
		}
	}

}
