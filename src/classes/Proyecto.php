<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

// Import PDOException for better error handling

class Proyecto
{
	// --- Atributos ---
	private ?int    $id              = null;
	private ?string $imagen_portada  = null;
	private ?string $logo_cliente    = null;
	private ?string $titulo          = null;
	private ?string $ubicacion       = null;
	private ?string $descripcion     = null;
	private ?int    $prioridad       = null;
	private ?int    $estado          = null;
	
	/**
	 * Constructor: Inicializa las propiedades del objeto Proyecto.
	 */
	public function __construct()
	{
		$this->id              = 0; // O null si prefieres no usar 0 por defecto
		$this->imagen_portada  = null;
		$this->logo_cliente    = null;
		$this->titulo          = null;
		$this->ubicacion       = null;
		$this->descripcion     = null;
		$this->prioridad       = 0;
		$this->estado          = 1; // Estado activo por defecto
	}
	
	/**
	 * Método estático para construir un objeto Proyecto desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del proyecto.
	 *
	 * @return self Instancia de Proyecto.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                  = new self();
			$objeto->id              = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->imagen_portada  = $resultado['imagen_portada'] ?? null;
			$objeto->logo_cliente    = $resultado['logo_cliente'] ?? null;
			$objeto->titulo          = $resultado['titulo'] ?? null;
			$objeto->ubicacion       = $resultado['ubicacion'] ?? null;
			$objeto->descripcion     = $resultado['descripcion'] ?? null;
			$objeto->prioridad       = isset($resultado['prioridad']) ? (int)$resultado['prioridad'] : 0;
			$objeto->estado          = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir Proyecto: " . $e->getMessage());
		}
	}
	
	// --- Métodos de Acceso a Datos (Estáticos) ---
	
	/**
	 * Obtiene un proyecto por su ID.
	 *
	 * @param int $id       ID del proyecto.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Proyecto o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener proyecto por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	p.*
            FROM proyectos p
            WHERE
            	p.id = :id
            LIMIT 1
            SQL;
			// Asume tabla 'proyectos'
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);
			
			return $resultado ? self::construct($resultado) : null;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener Proyecto (ID: $id): " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene una lista de proyectos activos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Proyecto.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de proyectos activos (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	p.*
            FROM proyectos p
            WHERE
            	p.estado = 1
            ORDER BY
            	p.prioridad ASC, p.titulo ASC
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);
			
			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Proyectos: " . $e->getMessage());
		}
	}
	
	/**
	 * Crea un nuevo proyecto en la base de datos a partir de un objeto Proyecto.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo proyecto creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getTitulo())) {
			throw new Exception("Título es requerido en el objeto Proyecto para crearlo.");
		}

		// Validaciones de negocio
		$this->validarReglasNegocio($conexion);

		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO proyectos (
            	 imagen_portada
            	,logo_cliente
            	,titulo
            	,ubicacion
            	,descripcion
            	,prioridad
            	,estado
            ) VALUES (
            	 :imagen_portada
            	,:logo_cliente
            	,:titulo
            	,:ubicacion
            	,:descripcion
            	,:prioridad
            	,:estado
            )
            SQL;
			// Asume tabla 'proyectos'

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':imagen_portada', $this->getImagenPortada(), PDO::PARAM_STR);
			$statement->bindValue(':logo_cliente', $this->getLogoCliente(), PDO::PARAM_STR);
			$statement->bindValue(':titulo', $this->getTitulo(), PDO::PARAM_STR);
			$statement->bindValue(':ubicacion', $this->getUbicacion(), PDO::PARAM_STR);
			$statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
			$statement->bindValue(':prioridad', $this->getPrioridad() ?? 0, PDO::PARAM_INT);
			$statement->bindValue(':estado', $this->getEstado() ?? 1, PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del proyecto recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al crear proyecto: " . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear proyecto: " . $e->getMessage());
		}
	}
	
	/**
	 * Modifica un proyecto existente.
	 *
	 * @param int    $id          ID del proyecto a modificar.
	 * @param string $nuevoTitulo El nuevo título para el proyecto.
	 * @param PDO    $conexion    Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si el nuevo título está vacío o si ocurre un error de base de datos.
	 */
	public static function modificar(int $id, string $nuevoTitulo, PDO $conexion): bool
	{
		if (empty(trim($nuevoTitulo))) {
			throw new Exception("El título no puede estar vacío.");
		}

		if (strlen(trim($nuevoTitulo)) < 2) {
			throw new Exception("El título debe tener al menos 2 caracteres.");
		}

		try {
			// Consulta para actualizar el título
			$query = <<<SQL
            UPDATE proyectos SET
                titulo = :titulo
            WHERE
                id = :id
                AND estado = 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':titulo', trim($nuevoTitulo), PDO::PARAM_STR);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			$result = $statement->execute();

			// Verificar si se actualizó alguna fila
			if ($result && $statement->rowCount() === 0) {
				throw new Exception("No se encontró el proyecto o no se realizaron cambios.");
			}

			return $result;

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al modificar proyecto (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Modifica un proyecto existente con todos los campos.
	 *
	 * @param int     $id             ID del proyecto a modificar.
	 * @param ?string $imagen_portada Nueva imagen de portada (null para mantener actual).
	 * @param ?string $logo_cliente   Nuevo logo del cliente (null para mantener actual).
	 * @param string  $titulo         Nuevo título.
	 * @param string  $ubicacion      Nueva ubicación.
	 * @param string  $descripcion    Nueva descripción.
	 * @param int     $prioridad      Nueva prioridad.
	 * @param PDO     $conexion       Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si los datos están vacíos o si ocurre un error de base de datos.
	 */
	public static function modificarCompleto(int $id, ?string $imagen_portada, ?string $logo_cliente, string $titulo, string $ubicacion, string $descripcion, int $prioridad, PDO $conexion): bool
	{
		if (empty(trim($titulo))) {
			throw new Exception("El título no puede estar vacío.");
		}

		try {
			// Build dynamic query based on what fields need updating
			$setParts = [];
			$params = [':id' => $id];

			if ($imagen_portada !== null) {
				$setParts[] = 'imagen_portada = :imagen_portada';
				$params[':imagen_portada'] = $imagen_portada;
			}

			if ($logo_cliente !== null) {
				$setParts[] = 'logo_cliente = :logo_cliente';
				$params[':logo_cliente'] = $logo_cliente;
			}

			$setParts[] = 'titulo = :titulo';
			$setParts[] = 'ubicacion = :ubicacion';
			$setParts[] = 'descripcion = :descripcion';
			$setParts[] = 'prioridad = :prioridad';

			$params[':titulo'] = trim($titulo);
			$params[':ubicacion'] = trim($ubicacion);
			$params[':descripcion'] = trim($descripcion);
			$params[':prioridad'] = $prioridad;

			$query = "UPDATE proyectos SET " . implode(', ', $setParts) . " WHERE id = :id AND estado = 1";

			$statement = $conexion->prepare($query);

			foreach ($params as $param => $value) {
				$statement->bindValue($param, $value);
			}

			$result = $statement->execute();

			// Verificar si se actualizó alguna fila
			if ($result && $statement->rowCount() === 0) {
				throw new Exception("No se encontró el proyecto o no se realizaron cambios.");
			}

			return $result;

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al modificar proyecto (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Alias for get_list() method for consistency with admin patterns.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Proyecto.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerTodos(PDO $conexion): array
	{
		return self::get_list($conexion);
	}

	/**
	 * Alias for get() method for consistency with admin patterns.
	 *
	 * @param int $id       ID del proyecto.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Proyecto o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorId(int $id, PDO $conexion): ?self
	{
		return self::get($id, $conexion);
	}

	/**
	 * Alias for desactivar() method for consistency with admin patterns.
	 *
	 * @param int $id       ID del proyecto a eliminar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la eliminación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function eliminar(int $id, PDO $conexion): bool
	{
		return self::desactivar($id, $conexion);
	}


	/**
	 * Desactiva un proyecto estableciendo su estado a 0.
	 *
	 * @param int $id       ID del proyecto a desactivar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desactivación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el estado a 0 (inactivo)
			$query = <<<SQL
            UPDATE proyectos SET
            	estado = 0
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			// Podríamos chequear rowCount() > 0 si queremos asegurarnos que una fila fue afectada.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			// Considera loggear el error aquí
			throw new Exception("Error de base de datos al desactivar proyecto (ID: $id): " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getImagenPortada(): ?string
	{
		return $this->imagen_portada;
	}

	public function setImagenPortada(?string $imagen_portada): self
	{
		$this->imagen_portada = $imagen_portada;
		return $this;
	}

	public function getLogoCliente(): ?string
	{
		return $this->logo_cliente;
	}

	public function setLogoCliente(?string $logo_cliente): self
	{
		$this->logo_cliente = $logo_cliente;
		return $this;
	}

	public function getTitulo(): ?string
	{
		return $this->titulo;
	}

	public function setTitulo(?string $titulo): self
	{
		$this->titulo = $titulo;
		return $this;
	}

	public function getUbicacion(): ?string
	{
		return $this->ubicacion;
	}

	public function setUbicacion(?string $ubicacion): self
	{
		$this->ubicacion = $ubicacion;
		return $this;
	}

	public function getDescripcion(): ?string
	{
		return $this->descripcion;
	}

	public function setDescripcion(?string $descripcion): self
	{
		$this->descripcion = $descripcion;
		return $this;
	}

	public function getPrioridad(): ?int
	{
		return $this->prioridad;
	}

	public function setPrioridad(?int $prioridad): self
	{
		$this->prioridad = $prioridad;
		return $this;
	}

	public function getEstado(): ?int
	{
		return $this->estado;
	}

	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	// --- Métodos adicionales (Ejemplos) ---

	/**
	 * Verifica si el proyecto está activo.
	 * @return bool
	 */
	public function isActivo(): bool
	{
		// Asegúrate de que el estado no sea null antes de comparar
		return $this->estado === 1;
	}

	/**
	 * Valida las reglas de negocio para la creación de proyectos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @throws Exception Si las validaciones fallan.
	 */
	private function validarReglasNegocio(PDO $conexion): void
	{
		// Validar longitud mínima del título
		if (strlen(trim($this->getTitulo())) < 2) {
			throw new Exception("El título debe tener al menos 2 caracteres.");
		}
	}

}
