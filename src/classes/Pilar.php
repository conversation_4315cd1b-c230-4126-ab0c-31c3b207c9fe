<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

// Import PDOException for better error handling

class Pilar
{
	// --- Atributos ---
	private ?int    $id              = null;
	private ?string $texto           = null;
	private ?string $pilar_imagen_1  = null;
	private ?string $pilar_titulo_1  = null;
	private ?string $pilar_texto_1   = null;
	private ?string $pilar_imagen_2  = null;
	private ?string $pilar_titulo_2  = null;
	private ?string $pilar_texto_2   = null;
	private ?string $pilar_imagen_3  = null;
	private ?string $pilar_titulo_3  = null;
	private ?string $pilar_texto_3   = null;

	/**
	 * Constructor: Inicializa las propiedades del objeto Pilar.
	 */
	public function __construct()
	{
		$this->id              = 0; // O null si prefieres no usar 0 por defecto
		$this->texto           = null;
		$this->pilar_imagen_1  = null;
		$this->pilar_titulo_1  = null;
		$this->pilar_texto_1   = null;
		$this->pilar_imagen_2  = null;
		$this->pilar_titulo_2  = null;
		$this->pilar_texto_2   = null;
		$this->pilar_imagen_3  = null;
		$this->pilar_titulo_3  = null;
		$this->pilar_texto_3   = null;
	}

	/**
	 * Método estático para construir un objeto Pilar desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del pilar.
	 *
	 * @return self Instancia de Pilar.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                   = new self();
			$objeto->id               = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->texto            = $resultado['texto'] ?? null;
			$objeto->pilar_imagen_1   = $resultado['pilar_imagen_1'] ?? null;
			$objeto->pilar_titulo_1   = $resultado['pilar_titulo_1'] ?? null;
			$objeto->pilar_texto_1    = $resultado['pilar_texto_1'] ?? null;
			$objeto->pilar_imagen_2   = $resultado['pilar_imagen_2'] ?? null;
			$objeto->pilar_titulo_2   = $resultado['pilar_titulo_2'] ?? null;
			$objeto->pilar_texto_2    = $resultado['pilar_texto_2'] ?? null;
			$objeto->pilar_imagen_3   = $resultado['pilar_imagen_3'] ?? null;
			$objeto->pilar_titulo_3   = $resultado['pilar_titulo_3'] ?? null;
			$objeto->pilar_texto_3    = $resultado['pilar_texto_3'] ?? null;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir Pilar: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos (Estáticos) ---

	/**
	 * Obtiene un pilar por su ID.
	 *
	 * @param int $id       ID del pilar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Pilar o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener pilar por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	p.*
            FROM pilares p
            WHERE
            	p.id = :id
            LIMIT 1
            SQL;
			// Asume tabla 'pilares'

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Pilar (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene el primer pilar disponible (pilar principal).
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Pilar o null si no existe pilar.
	 * @throws Exception Si hay error en DB.
	 */
	public static function getPrincipal(PDO $conexion): ?self
	{
		try {
			$query = <<<SQL
            SELECT
                p.*
            FROM pilares p
            ORDER BY
                p.id ASC
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener pilar principal: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de pilares ordenados por ID.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Pilar.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de pilares ordenados por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	p.*
            FROM pilares p
            ORDER BY
            	p.id ASC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Pilares: " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo pilar en la base de datos a partir de un objeto Pilar.
	 * El objeto Pilar debe estar completamente poblado con al menos el texto principal.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo pilar creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getTexto())) {
			throw new Exception("El texto principal es requerido en el objeto Pilar para crearlo.");
		}

		// Validaciones de negocio
		$this->validarReglasNegocio($conexion);

		try {
			$texto = $this->getTexto(); // Para usar en mensaje de error

			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO pilares (
            	 texto
            	,pilar_imagen_1
            	,pilar_titulo_1
            	,pilar_texto_1
            	,pilar_imagen_2
            	,pilar_titulo_2
            	,pilar_texto_2
            	,pilar_imagen_3
            	,pilar_titulo_3
            	,pilar_texto_3
            ) VALUES (
            	 :texto
            	,:pilar_imagen_1
            	,:pilar_titulo_1
            	,:pilar_texto_1
            	,:pilar_imagen_2
            	,:pilar_titulo_2
            	,:pilar_texto_2
            	,:pilar_imagen_3
            	,:pilar_titulo_3
            	,:pilar_texto_3
            )
            SQL;
			// Asume tabla 'pilares'

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':texto', $this->getTexto(), PDO::PARAM_STR);
			$statement->bindValue(':pilar_imagen_1', $this->getPilarImagen1(), PDO::PARAM_STR);
			$statement->bindValue(':pilar_titulo_1', $this->getPilarTitulo1(), PDO::PARAM_STR);
			$statement->bindValue(':pilar_texto_1', $this->getPilarTexto1(), PDO::PARAM_STR);
			$statement->bindValue(':pilar_imagen_2', $this->getPilarImagen2(), PDO::PARAM_STR);
			$statement->bindValue(':pilar_titulo_2', $this->getPilarTitulo2(), PDO::PARAM_STR);
			$statement->bindValue(':pilar_texto_2', $this->getPilarTexto2(), PDO::PARAM_STR);
			$statement->bindValue(':pilar_imagen_3', $this->getPilarImagen3(), PDO::PARAM_STR);
			$statement->bindValue(':pilar_titulo_3', $this->getPilarTitulo3(), PDO::PARAM_STR);
			$statement->bindValue(':pilar_texto_3', $this->getPilarTexto3(), PDO::PARAM_STR);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del pilar recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			// Manejar errores específicos de DB
			throw new Exception("Error de base de datos al crear pilar: " . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear pilar: " . $e->getMessage());
		}
	}

	/**
	 * Modifica un pilar existente.
	 *
	 * @param int         $id             ID del pilar a modificar.
	 * @param string      $texto          Nuevo texto principal para el pilar.
	 * @param string|null $pilar_imagen_1 Nueva imagen para el pilar 1 (opcional).
	 * @param string|null $pilar_titulo_1 Nuevo título para el pilar 1 (opcional).
	 * @param string|null $pilar_texto_1  Nuevo texto para el pilar 1 (opcional).
	 * @param string|null $pilar_imagen_2 Nueva imagen para el pilar 2 (opcional).
	 * @param string|null $pilar_titulo_2 Nuevo título para el pilar 2 (opcional).
	 * @param string|null $pilar_texto_2  Nuevo texto para el pilar 2 (opcional).
	 * @param string|null $pilar_imagen_3 Nueva imagen para el pilar 3 (opcional).
	 * @param string|null $pilar_titulo_3 Nuevo título para el pilar 3 (opcional).
	 * @param string|null $pilar_texto_3  Nuevo texto para el pilar 3 (opcional).
	 * @param PDO         $conexion       Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si el texto principal está vacío o si ocurre un error de base de datos.
	 */
	public static function modificar(
		int $id,
		string $texto,
		?string $pilar_imagen_1,
		?string $pilar_titulo_1,
		?string $pilar_texto_1,
		?string $pilar_imagen_2,
		?string $pilar_titulo_2,
		?string $pilar_texto_2,
		?string $pilar_imagen_3,
		?string $pilar_titulo_3,
		?string $pilar_texto_3,
		PDO $conexion
	): bool {
		if (empty(trim($texto))) {
			throw new Exception("El texto principal no puede estar vacío.");
		}

		if (strlen(trim($texto)) < 2) {
			throw new Exception("El texto principal debe tener al menos 2 caracteres.");
		}

		try {
			// Consulta para actualizar el pilar
			$query = <<<SQL
            UPDATE pilares SET
                texto = :texto,
                pilar_imagen_1 = :pilar_imagen_1,
                pilar_titulo_1 = :pilar_titulo_1,
                pilar_texto_1 = :pilar_texto_1,
                pilar_imagen_2 = :pilar_imagen_2,
                pilar_titulo_2 = :pilar_titulo_2,
                pilar_texto_2 = :pilar_texto_2,
                pilar_imagen_3 = :pilar_imagen_3,
                pilar_titulo_3 = :pilar_titulo_3,
                pilar_texto_3 = :pilar_texto_3
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':texto', trim($texto), PDO::PARAM_STR);
			$statement->bindValue(':pilar_imagen_1', $pilar_imagen_1, PDO::PARAM_STR);
			$statement->bindValue(':pilar_titulo_1', $pilar_titulo_1, PDO::PARAM_STR);
			$statement->bindValue(':pilar_texto_1', $pilar_texto_1, PDO::PARAM_STR);
			$statement->bindValue(':pilar_imagen_2', $pilar_imagen_2, PDO::PARAM_STR);
			$statement->bindValue(':pilar_titulo_2', $pilar_titulo_2, PDO::PARAM_STR);
			$statement->bindValue(':pilar_texto_2', $pilar_texto_2, PDO::PARAM_STR);
			$statement->bindValue(':pilar_imagen_3', $pilar_imagen_3, PDO::PARAM_STR);
			$statement->bindValue(':pilar_titulo_3', $pilar_titulo_3, PDO::PARAM_STR);
			$statement->bindValue(':pilar_texto_3', $pilar_texto_3, PDO::PARAM_STR);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			$result = $statement->execute();

			// Verificar si se actualizó alguna fila
			if ($result && $statement->rowCount() === 0) {
				throw new Exception("No se encontró el pilar o no se realizaron cambios.");
			}

			return $result;

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al modificar pilar (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Elimina un pilar de la base de datos.
	 * Nota: Como la tabla pilares no tiene campo estado, se realiza eliminación física.
	 *
	 * @param int $id       ID del pilar a eliminar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la eliminación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function eliminar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para eliminar el pilar físicamente
			$query = <<<SQL
            DELETE FROM pilares
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			// Podríamos chequear rowCount() > 0 si queremos asegurarnos que una fila fue afectada.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			// Considera loggear el error aquí
			throw new Exception("Error de base de datos al eliminar pilar (ID: $id): " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getTexto(): ?string
	{
		return $this->texto;
	}

	public function setTexto(?string $texto): self
	{
		$this->texto = $texto;
		return $this;
	}

	public function getPilarImagen1(): ?string
	{
		return $this->pilar_imagen_1;
	}

	public function setPilarImagen1(?string $pilar_imagen_1): self
	{
		$this->pilar_imagen_1 = $pilar_imagen_1;
		return $this;
	}

	public function getPilarTitulo1(): ?string
	{
		return $this->pilar_titulo_1;
	}

	public function setPilarTitulo1(?string $pilar_titulo_1): self
	{
		$this->pilar_titulo_1 = $pilar_titulo_1;
		return $this;
	}

	public function getPilarTexto1(): ?string
	{
		return $this->pilar_texto_1;
	}

	public function setPilarTexto1(?string $pilar_texto_1): self
	{
		$this->pilar_texto_1 = $pilar_texto_1;
		return $this;
	}

	public function getPilarImagen2(): ?string
	{
		return $this->pilar_imagen_2;
	}

	public function setPilarImagen2(?string $pilar_imagen_2): self
	{
		$this->pilar_imagen_2 = $pilar_imagen_2;
		return $this;
	}

	public function getPilarTitulo2(): ?string
	{
		return $this->pilar_titulo_2;
	}

	public function setPilarTitulo2(?string $pilar_titulo_2): self
	{
		$this->pilar_titulo_2 = $pilar_titulo_2;
		return $this;
	}

	public function getPilarTexto2(): ?string
	{
		return $this->pilar_texto_2;
	}

	public function setPilarTexto2(?string $pilar_texto_2): self
	{
		$this->pilar_texto_2 = $pilar_texto_2;
		return $this;
	}

	public function getPilarImagen3(): ?string
	{
		return $this->pilar_imagen_3;
	}

	public function setPilarImagen3(?string $pilar_imagen_3): self
	{
		$this->pilar_imagen_3 = $pilar_imagen_3;
		return $this;
	}

	public function getPilarTitulo3(): ?string
	{
		return $this->pilar_titulo_3;
	}

	public function setPilarTitulo3(?string $pilar_titulo_3): self
	{
		$this->pilar_titulo_3 = $pilar_titulo_3;
		return $this;
	}

	public function getPilarTexto3(): ?string
	{
		return $this->pilar_texto_3;
	}

	public function setPilarTexto3(?string $pilar_texto_3): self
	{
		$this->pilar_texto_3 = $pilar_texto_3;
		return $this;
	}

	// --- Métodos adicionales (Ejemplos) ---

	/**
	 * Valida las reglas de negocio para la creación de pilares.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @throws Exception Si las validaciones fallan.
	 */
	private function validarReglasNegocio(PDO $conexion): void
	{
		// Validar longitud mínima del texto principal
		if (strlen(trim($this->getTexto())) < 2) {
			throw new Exception("El texto principal debe tener al menos 2 caracteres.");
		}

		// Validar longitud máxima de las imágenes si se proporcionan
		if ($this->getPilarImagen1() !== null && strlen($this->getPilarImagen1()) > 255) {
			throw new Exception("La ruta de la imagen del pilar 1 no puede exceder los 255 caracteres.");
		}

		if ($this->getPilarImagen2() !== null && strlen($this->getPilarImagen2()) > 255) {
			throw new Exception("La ruta de la imagen del pilar 2 no puede exceder los 255 caracteres.");
		}

		if ($this->getPilarImagen3() !== null && strlen($this->getPilarImagen3()) > 255) {
			throw new Exception("La ruta de la imagen del pilar 3 no puede exceder los 255 caracteres.");
		}

		// Validar que al menos un pilar tenga título si se proporciona
		$titulos = [
			$this->getPilarTitulo1(),
			$this->getPilarTitulo2(),
			$this->getPilarTitulo3()
		];

		$titulosValidos = array_filter($titulos, function($titulo) {
			return $titulo !== null && strlen(trim($titulo)) > 0;
		});

		if (empty($titulosValidos)) {
			throw new Exception("Al menos uno de los pilares debe tener un título.");
		}
	}

}
