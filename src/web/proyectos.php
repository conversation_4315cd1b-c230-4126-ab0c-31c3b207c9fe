<?php session_start();

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar_web.php';

use App\classes\Proyecto;
use App\classes\ProyectoImagen;

/**
 * Determines the media type based on file extension
 *
 * @param string $filename The filename to analyze
 *
 * @return string 'image' or 'video'
 */
function getMediaType(string $filename): string
{
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

    $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
    $videoExtensions = ['mp4', 'webm', 'ogg', 'mov', 'avi'];

    if (in_array($extension, $videoExtensions)) {
        return 'video';
    } elseif (in_array($extension, $imageExtensions)) {
        return 'image';
    }

    // Default to image for unknown extensions
    return 'image';
}

/**
 * Processes media files array to include type information
 * @param array $files Array of filenames
 * @return array Array of media objects with file and type properties
 */
function processMediaFiles($files): array
{
    $processedFiles = [];
    foreach ($files as $file) {
        $processedFiles[] = [
            'file' => $file,
            'type' => getMediaType($file)
        ];
    }
    return $processedFiles;
}

#region region init variables
// Load projects from database
try {
    $proyectosDB = Proyecto::obtenerTodos($conexion);
    $proyectos = [];

    foreach ($proyectosDB as $proyectoDB) {
        // Get project images
        $imagenes = ProyectoImagen::obtenerPorProyecto($proyectoDB->getId(), $conexion);
        
        // Build images array for compatibility with existing view
        $images = [];
        foreach ($imagenes as $imagen) {
            $images[] = $imagen->getImagen();
        }
        
        // Convert database project to array format expected by view
        $proyectos[] = [
            'id' => $proyectoDB->getId(),
            'client_name' => '', // This will be extracted from title or can be added as separate field
            'project_name' => $proyectoDB->getTitulo(),
            'location' => $proyectoDB->getUbicacion(),
            'description' => $proyectoDB->getDescripcion(),
            'client_logo' => $proyectoDB->getLogoCliente() ?? 'default-logo.png',
            'featured_image' => $proyectoDB->getImagenPortada() ?? ($images[0] ?? 'default-project.jpg'),
            'images' => $images
        ];
    }
} catch (Exception $e) {
    error_log("Error loading projects: " . $e->getMessage());
    // Fallback to empty array if database fails
    $proyectos = [];
}

// Process media files for each project to include type information
foreach ($proyectos as &$proyecto) {
    $proyecto['media'] = processMediaFiles($proyecto['images']);
}
unset($proyecto); // Important: unset the reference to avoid issues
#endregion init variables

require_once __ROOT__ . '/views/web/proyectos.view.php';

?>
