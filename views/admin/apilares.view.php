<?php
#region region DOCS

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Administrar Pilares</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/head.view.php'; ?>
	<link href="<?php echo RUTA_RESOURCES ?>css/apilares.css" rel="stylesheet" />
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<h4>Administrar Pilares</h4>

		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region PILARES FORM ?>
		<form action="admin-pilares" method="POST" id="pilares-form" enctype="multipart/form-data" novalidate>
			<div class="panel panel-inverse no-border-radious">
				<div class="panel-heading no-border-radious">
					<h4 class="panel-title">Configuración de Pilares</h4>
					<div class="panel-heading-btn">
						<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
						<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
					</div>
				</div>
				<div class="panel-body">
					<?php #region region MAIN DESCRIPTION ?>
					<div class="row">
						<div class="col-12">
							<div class="mb-3">
								<label for="texto" class="form-label">Descripción Principal:</label>
								<textarea id="texto"
										  name="texto"
										  rows="7"
										  class="form-control"
										  placeholder="Ingrese la descripción principal que aparecerá en la sección '¿Por qué elegir a Serimpro S.A.S.?'"
										  required><?php echo htmlspecialchars($pilarData ? $pilarData->getTexto() ?? '' : '', ENT_QUOTES, 'UTF-8'); ?></textarea>
								<div class="invalid-feedback">
									Por favor, ingrese la descripción principal.
								</div>
							</div>
						</div>
					</div>
					<?php #endregion MAIN DESCRIPTION ?>

					<hr class="my-4">

					<?php #region region PILARES SECTIONS ?>
					<?php for ($i = 1; $i <= 3; $i++): ?>
						<div class="pilar-section mb-4">
							<h5 class="text-primary mb-3">
								<i class="fa fa-pillar-sign me-2"></i>
								Pilar <?php echo $i; ?>
							</h5>
							
							<div class="row">
								<div class="col-md-4">
									<?php #region region IMAGE UPLOAD ?>
									<div class="mb-3">
										<label for="pilar_imagen_<?php echo $i; ?>" class="form-label">Imagen del Pilar <?php echo $i; ?>:</label>
										<input type="file"
											   id="pilar_imagen_<?php echo $i; ?>"
											   name="pilar_imagen_<?php echo $i; ?>"
											   class="form-control"
											   accept="image/jpeg,image/jpg,image/png"/>
										<div class="form-text">
											<small>Recomendado: 200x200 píxeles. Máximo: 600KB. Solo JPG/PNG.</small>
										</div>
										<?php if ($pilarData && $pilarData->{"getPilarImagen$i"}()): ?>
											<div class="mt-2">
												<img src="<?php echo RUTA_RESOURCES; ?>images/pilares/<?php echo htmlspecialchars($pilarData->{"getPilarImagen$i"}()); ?>"
													 alt="Imagen actual del pilar <?php echo $i; ?>"
													 class="img-thumbnail"
													 style="max-width: 100px; max-height: 100px;">
												<small class="text-muted d-block">Imagen actual</small>
											</div>
										<?php endif; ?>
										<div class="invalid-feedback">
											Por favor, seleccione una imagen válida (JPG/PNG, máximo 600KB).
										</div>
									</div>
									<?php #endregion IMAGE UPLOAD ?>
								</div>
								
								<div class="col-md-8">
									<?php #region region TITLE FIELD ?>
									<div class="mb-3">
										<label for="pilar_titulo_<?php echo $i; ?>" class="form-label">Título del Pilar <?php echo $i; ?>:</label>
										<input type="text"
											   id="pilar_titulo_<?php echo $i; ?>"
											   name="pilar_titulo_<?php echo $i; ?>"
											   value="<?php echo htmlspecialchars($pilarData ? $pilarData->{"getPilarTitulo$i"}() ?? '' : '', ENT_QUOTES, 'UTF-8'); ?>"
											   class="form-control"
											   placeholder="Ej: Excelencia Técnica"/>
										<div class="invalid-feedback">
											Por favor, ingrese un título válido.
										</div>
									</div>
									<?php #endregion TITLE FIELD ?>

									<?php #region region TEXT FIELD ?>
									<div class="mb-3">
										<label for="pilar_texto_<?php echo $i; ?>" class="form-label">Descripción del Pilar <?php echo $i; ?>:</label>
										<textarea id="pilar_texto_<?php echo $i; ?>"
												  name="pilar_texto_<?php echo $i; ?>"
												  rows="5"
												  class="form-control"
												  placeholder="Ingrese la descripción detallada del pilar <?php echo $i; ?>"><?php echo htmlspecialchars($pilarData ? $pilarData->{"getPilarTexto$i"}() ?? '' : '', ENT_QUOTES, 'UTF-8'); ?></textarea>
										<div class="invalid-feedback">
											Por favor, ingrese una descripción válida.
										</div>
									</div>
									<?php #endregion TEXT FIELD ?>
								</div>
							</div>
						</div>
						
						<?php if ($i < 3): ?>
							<hr class="my-4">
						<?php endif; ?>
					<?php endfor; ?>
					<?php #endregion PILARES SECTIONS ?>

					<div class="row">
						<div class="col-12">
							<div class="alert alert-info">
								<i class="fa fa-info-circle"></i>
								<strong>Nota:</strong> Los cambios se reflejarán automáticamente en la página principal en la sección "¿Por qué elegir a Serimpro S.A.S.?".
							</div>
						</div>
					</div>
				</div>
				<div class="panel-footer text-end">
					<button type="submit" class="btn btn-primary">
						<i class="fa fa-save fa-fw me-1"></i> Guardar Pilares
					</button>
				</div>
			</div>
		</form>
		<?php #endregion PILARES FORM ?>
	</div>
	<!-- END #content -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/core_js.view.php'; ?>

<script src="<?php echo RUTA_RESOURCES ?>js/apilares.js"></script>

<?php #endregion JS ?>

</body>
</html>
