<?php
#region region DOCS

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Proyectos</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/head.view.php'; ?>
	<link href="<?php echo RUTA_RESOURCES ?>css/dashboard.css" rel="stylesheet"/>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed ">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/sidebar.view.php'; ?>
	<!-- END #sidebar -->

	<!-- BEGIN #content -->
	<div id="content" class="app-content">

		<!-- BEGIN page-header -->
		<h1 class="page-header">Gestión de Proyectos <small>Administrar proyectos del sitio web</small></h1>
		<!-- END page-header -->

            <?php #region region PROYECTOS FORM ?>
            <div class="panel panel-inverse no-border-radious">
                <div class="panel-heading no-border-radious">
                    <h4 class="panel-title">Gestión de Proyectos</h4>
                    <div class="panel-heading-btn">
                        <a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
                        <a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="row mb-3">
                        <div class="col-12 text-end">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalProyecto" onclick="abrirModalCrear()">
                                <i class="fa fa-plus fa-fw me-1"></i> Crear Nuevo Proyecto
                            </button>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered align-middle">
                            <thead>
                            <tr>
                                <th width="150" class="text-center">Acciones</th>
                                <th width="150" class="text-center">Portada</th>
                                <th width="100" class="text-center">Logo Cliente</th>
                                <th>Título</th>
                                <th>Ubicación</th>
                                <th>Descripción</th>
                                <th width="100" class="text-center">Prioridad</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php if (!empty($proyectos)): ?>
                                <?php foreach ($proyectos as $proyecto): ?>
                                    <tr>
                                        <td class="text-center">
                                            <button type="button" class="btn btn-xs btn-info me-1"
                                                    onclick="gestionarImagenes(<?php echo $proyecto->getId(); ?>)"
                                                    title="Gestionar imágenes">
                                                <i class="fa fa-images"></i>
                                            </button>
                                            <button type="button" class="btn btn-xs btn-warning me-1"
                                                    onclick="abrirModalEditar(<?php echo $proyecto->getId(); ?>)"
                                                    title="Editar proyecto">
                                                <i class="fa fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-xs btn-danger"
                                                    onclick="eliminarProyecto(<?php echo $proyecto->getId(); ?>)"
                                                    title="Eliminar proyecto">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        </td>
                                        <td class="text-center">
                                            <?php if ($proyecto->getImagenPortada()): ?>
                                                <img src="<?php echo RUTA_RESOURCES ?>images/proyectos/<?php echo htmlspecialchars($proyecto->getImagenPortada()); ?>"
                                                     alt="Portada" class="proyecto-thumbnail" style="max-width: 100px; max-height: 50px; object-fit: contain;">
                                            <?php else: ?>
                                                <span class="text-muted">Sin portada</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-center">
                                            <?php if ($proyecto->getLogoCliente()): ?>
                                                <img src="<?php echo RUTA_RESOURCES ?>images/proyectos/logos/<?php echo htmlspecialchars($proyecto->getLogoCliente()); ?>"
                                                     alt="Logo" class="logo-thumbnail" style="max-width: 60px; max-height: 40px; object-fit: contain;">
                                            <?php else: ?>
                                                <span class="text-muted">Sin logo</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($proyecto->getTitulo()); ?></strong>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($proyecto->getUbicacion()); ?>
                                        </td>
                                        <td>
                                            <div style="max-width: 300px; word-wrap: break-word; white-space: normal;">
                                                <?php echo htmlspecialchars($proyecto->getDescripcion()); ?>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-primary"><?php echo $proyecto->getPrioridad(); ?></span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
								<tr>
									<td colspan="7" class="text-center text-muted">
										<i class="fa fa-info-circle me-2"></i>No hay proyectos registrados
									</td>
								</tr>
                            <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php #endregion PROYECTOS FORM ?>
	</div>
	<!-- END #content -->

	<!-- BEGIN scroll-top-btn -->
	<a href="javascript:;" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region MODAL PROYECTO ?>
<div class="modal fade" id="modalProyecto" tabindex="-1" aria-labelledby="modalProyectoLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form id="formProyecto" enctype="multipart/form-data">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalProyectoLabel">Crear Nuevo Proyecto</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="action" name="action" value="crear">
                    <input type="hidden" id="id" name="id" value="">
                    
                    <div class="row">
                        <!-- Portada Upload -->
                        <div class="col-md-6 mb-3">
                            <label for="portada" class="form-label">
                                Portada <span class="text-danger">*</span>
                            </label>
                            <input type="file" class="form-control" id="portada" name="portada" accept="image/*">
                            <div class="form-text">
                                <i class="fa fa-info-circle"></i>
                                Máximo 2MB. Recomendado: 1000x650 píxeles. Formatos: JPG, PNG, WebP.
                            </div>
                            <div class="invalid-feedback"></div>
                            
                            <!-- Current image preview for edit -->
                            <div id="currentPortadaPreview" class="mt-2" style="display: none;">
                                <label class="form-label">Imagen actual:</label><br>
                                <img id="currentPortada" src="" alt="Portada actual" style="max-width: 150px; max-height: 100px; object-fit: contain; border: 1px solid #ddd; padding: 4px;">
                            </div>
                        </div>
                        
                        <!-- Logo Cliente Upload -->
                        <div class="col-md-6 mb-3">
                            <label for="logo_cliente" class="form-label">
                                Logo del Cliente <span class="text-danger">*</span>
                            </label>
                            <input type="file" class="form-control" id="logo_cliente" name="logo_cliente" accept="image/*">
                            <div class="form-text">
                                <i class="fa fa-info-circle"></i>
                                Máximo 600KB. Recomendado: 250x250 píxeles. Formatos: JPG, PNG, WebP.
                            </div>
                            <div class="invalid-feedback"></div>
                            
                            <!-- Current logo preview for edit -->
                            <div id="currentLogoPreview" class="mt-2" style="display: none;">
                                <label class="form-label">Logo actual:</label><br>
                                <img id="currentLogo" src="" alt="Logo actual" style="max-width: 100px; max-height: 80px; object-fit: contain; border: 1px solid #ddd; padding: 4px;">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- Título -->
                        <div class="col-md-6 mb-3">
                            <label for="titulo" class="form-label">
                                Título <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="titulo" name="titulo" maxlength="200" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        
                        <!-- Ubicación -->
                        <div class="col-md-6 mb-3">
                            <label for="ubicacion" class="form-label">
                                Ubicación <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="ubicacion" name="ubicacion" maxlength="200" required>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>
                    
                    <!-- Descripción -->
                    <div class="mb-3">
                        <label for="descripcion" class="form-label">
                            Descripción <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control" id="descripcion" name="descripcion" rows="7" maxlength="2000" required></textarea>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <!-- Prioridad -->
                    <div class="mb-3">
                        <label for="prioridad" class="form-label">
                            Prioridad <span class="text-danger">*</span>
                        </label>
                        <input type="number" class="form-control" id="prioridad" name="prioridad" min="1" max="999" required>
                        <div class="form-text">
                            <i class="fa fa-info-circle"></i>
                            Número del 1 al 999. Los proyectos se ordenan de menor a mayor prioridad.
                        </div>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i>
                        <strong>Nota:</strong> Los proyectos se mostrarán en el sitio web ordenados por prioridad (menor a mayor).
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary" id="btnSubmit">
                        <span class="spinner-border spinner-border-sm me-1 d-none" id="loadingSpinner"></span>
                        Crear
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php #endregion MODAL PROYECTO ?>

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/core_js.view.php'; ?>

<script src="<?php echo RUTA_RESOURCES ?>js/lproyectos.js"></script>

<?php #endregion JS ?>
</body>
</html>
