/* Pilares Admin Page Styles */

/* Pilar Section Styling */
.pilar-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.pilar-section:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
}

.pilar-section h5 {
    color: #007bff;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid rgba(0, 123, 255, 0.3);
}

/* Form Styling */
#pilares-form .form-label {
    color: #e9ecef;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

#pilares-form .form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    transition: all 0.3s ease;
}

#pilares-form .form-control:focus {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    color: #fff;
}

#pilares-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* Textarea Styling */
#pilares-form textarea.form-control {
    resize: vertical;
    min-height: 120px;
}

#pilares-form textarea#texto {
    min-height: 180px;
}

/* File Input Styling */
#pilares-form input[type="file"] {
    background-color: rgba(255, 255, 255, 0.1);
    border: 2px dashed rgba(255, 255, 255, 0.3);
    padding: 10px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

#pilares-form input[type="file"]:hover {
    border-color: rgba(0, 123, 255, 0.5);
    background-color: rgba(0, 123, 255, 0.1);
}

#pilares-form input[type="file"]:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Form Text Styling */
.form-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
}

/* Image Preview Styling */
.img-thumbnail {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 4px;
    border-radius: 6px;
}

/* Alert Styling */
.alert-info {
    background-color: rgba(23, 162, 184, 0.2);
    border-color: rgba(23, 162, 184, 0.3);
    color: #b8daff;
}

.alert-info .fa {
    color: #17a2b8;
}

/* Panel Styling */
.panel-inverse {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-inverse .panel-heading {
    background-color: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-inverse .panel-title {
    color: #fff;
    font-weight: 600;
}

.panel-inverse .panel-footer {
    background-color: rgba(255, 255, 255, 0.05);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Button Styling */
.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

/* Validation Styling */
.is-invalid {
    border-color: #dc3545 !important;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .pilar-section {
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .pilar-section h5 {
        font-size: 1.1rem;
    }
    
    #pilares-form textarea#texto {
        min-height: 150px;
    }
    
    #pilares-form textarea.form-control {
        min-height: 100px;
    }
}

/* Loading State */
.form-loading {
    opacity: 0.6;
    pointer-events: none;
}

.form-loading .btn {
    position: relative;
}

.form-loading .btn::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.alert-success {
    background-color: rgba(40, 167, 69, 0.2);
    border-color: rgba(40, 167, 69, 0.3);
    color: #d4edda;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.2);
    border-color: rgba(220, 53, 69, 0.3);
    color: #f8d7da;
}

/* Dark Mode Enhancements */
.dark-mode .text-muted {
    color: rgba(255, 255, 255, 0.6) !important;
}

.dark-mode hr {
    border-color: rgba(255, 255, 255, 0.2);
}

/* Icon Styling */
.fa-pillar-sign::before {
    content: "\f0db"; /* Using a column icon as pillar representation */
}
