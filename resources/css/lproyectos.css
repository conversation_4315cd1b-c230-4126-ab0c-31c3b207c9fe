/**
 * PROYECTOS ADMIN MODULE STYLES
 * Custom styles for the proyectos management interface
 */

/* Project thumbnail styling */
.proyecto-thumbnail {
    border-radius: 4px;
    border: 1px solid #495057;
    padding: 4px;
    background: #fff;
    transition: all 0.3s ease;
}

.proyecto-thumbnail:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

/* Logo thumbnail styling */
.logo-thumbnail {
    border-radius: 4px;
    border: 1px solid #495057;
    padding: 4px;
    background: #fff;
    transition: all 0.3s ease;
}

.logo-thumbnail:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

/* Loading state styling */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Priority badge styling */
.badge.bg-primary {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

/* Table action buttons */
.table td.text-center .btn {
    margin: 0 2px;
}

/* Form validation styling */
.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

/* Modal form enhancements */
#modalProyecto .form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

#modalProyecto .text-danger {
    color: #dc3545 !important;
}

#modalProyecto .form-text {
    font-size: 0.875em;
    color: #6c757d;
}

/* File input styling */
.form-control[type="file"] {
    padding: 0.375rem 0.75rem;
}

/* Image preview styling */
#currentPortadaPreview img,
#currentLogoPreview img {
    border-radius: 4px;
    transition: all 0.3s ease;
}

#currentPortadaPreview img:hover,
#currentLogoPreview img:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Alert styling */
.alert-info {
    background-color: rgba(13, 202, 240, 0.1);
    border-color: rgba(13, 202, 240, 0.2);
    color: #0dcaf0;
}

/* Table responsive enhancements */
.table-responsive {
    border-radius: 0.375rem;
}

.table th {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
    color: #e9ecef;
    font-weight: 600;
}

.table td {
    border-color: rgba(255, 255, 255, 0.1);
    vertical-align: middle;
}

/* Button styling */
.btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}

/* Modal enhancements */
.modal-content {
    background-color: #2a2a2a;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
    color: #e9ecef;
}

/* Form control dark theme */
.form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
}

.form-control:focus {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    color: #fff;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* Textarea specific styling */
textarea.form-control {
    resize: vertical;
    min-height: calc(1.5em + 0.75rem + 2px);
}

/* Number input styling */
input[type="number"].form-control {
    -moz-appearance: textfield;
}

input[type="number"].form-control::-webkit-outer-spin-button,
input[type="number"].form-control::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* File input dark theme */
.form-control[type="file"] {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
}

.form-control[type="file"]:focus {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Panel styling */
.panel-inverse {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-heading {
    background-color: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-title {
    color: #e9ecef;
    font-weight: 600;
}

/* Breadcrumb styling */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item {
    color: rgba(255, 255, 255, 0.7);
}

.breadcrumb-item.active {
    color: #e9ecef;
}

.breadcrumb-item + .breadcrumb-item::before {
    color: rgba(255, 255, 255, 0.5);
}

/* Page header styling */
.page-header {
    color: #e9ecef;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

.page-header small {
    color: rgba(255, 255, 255, 0.7);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-xs {
        padding: 0.2rem 0.4rem;
        font-size: 0.7rem;
    }
    
    .proyecto-thumbnail,
    .logo-thumbnail {
        max-width: 80px !important;
        max-height: 40px !important;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
}

/* Print styles */
@media print {
    .btn,
    .modal,
    .panel-heading-btn {
        display: none !important;
    }
    
    .table {
        color: #000 !important;
    }
    
    .panel-inverse {
        background-color: #fff !important;
        border: 1px solid #000 !important;
    }
}
