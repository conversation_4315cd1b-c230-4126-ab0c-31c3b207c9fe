/**
 * Pilares Admin Page JavaScript
 * Handles form validation, image uploads, and user interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize form validation
    initializeFormValidation();
    
    // Initialize image upload handlers
    initializeImageUploads();
    
    // Initialize form submission
    initializeFormSubmission();
});

/**
 * Initialize form validation
 */
function initializeFormValidation() {
    const form = document.getElementById('pilares-form');
    if (!form) return;

    // Add real-time validation
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        field.addEventListener('blur', validateField);
        field.addEventListener('input', clearFieldError);
    });

    // Validate main description
    const textoField = document.getElementById('texto');
    if (textoField) {
        textoField.addEventListener('input', function() {
            const value = this.value.trim();
            if (value.length < 2 && value.length > 0) {
                showFieldError(this, 'El texto principal debe tener al menos 2 caracteres.');
            } else {
                clearFieldError.call(this);
            }
        });
    }
}

/**
 * Initialize image upload handlers
 */
function initializeImageUploads() {
    for (let i = 1; i <= 3; i++) {
        const imageInput = document.getElementById(`pilar_imagen_${i}`);
        if (imageInput) {
            imageInput.addEventListener('change', function() {
                validateImageFile(this, i);
            });
        }
    }
}

/**
 * Validate image file
 */
function validateImageFile(input, pilarNumber) {
    const file = input.files[0];
    if (!file) return;

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!allowedTypes.includes(file.type)) {
        showFieldError(input, `Solo se permiten archivos JPG y PNG para la imagen del pilar ${pilarNumber}.`);
        input.value = '';
        return false;
    }

    // Check file size (600KB = 614400 bytes)
    if (file.size > 614400) {
        showFieldError(input, `La imagen del pilar ${pilarNumber} no puede exceder los 600KB.`);
        input.value = '';
        return false;
    }

    // Show preview if valid
    showImagePreview(input, file, pilarNumber);
    clearFieldError.call(input);
    return true;
}

/**
 * Show image preview
 */
function showImagePreview(input, file, pilarNumber) {
    const reader = new FileReader();
    reader.onload = function(e) {
        // Remove existing preview
        const existingPreview = input.parentNode.querySelector('.image-preview');
        if (existingPreview) {
            existingPreview.remove();
        }

        // Create new preview
        const preview = document.createElement('div');
        preview.className = 'image-preview mt-2';
        preview.innerHTML = `
            <img src="${e.target.result}" 
                 alt="Vista previa del pilar ${pilarNumber}" 
                 class="img-thumbnail" 
                 style="max-width: 100px; max-height: 100px;">
            <small class="text-muted d-block">Vista previa</small>
        `;
        
        input.parentNode.appendChild(preview);
    };
    reader.readAsDataURL(file);
}

/**
 * Initialize form submission
 */
function initializeFormSubmission() {
    const form = document.getElementById('pilares-form');
    if (!form) return;

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (validateForm()) {
            submitForm();
        }
    });
}

/**
 * Validate entire form
 */
function validateForm() {
    let isValid = true;
    const form = document.getElementById('pilares-form');

    // Clear previous errors
    clearAllErrors();

    // Validate main description
    const textoField = document.getElementById('texto');
    if (textoField) {
        const value = textoField.value.trim();
        if (!value) {
            showFieldError(textoField, 'El texto principal es requerido.');
            isValid = false;
        } else if (value.length < 2) {
            showFieldError(textoField, 'El texto principal debe tener al menos 2 caracteres.');
            isValid = false;
        }
    }

    // Validate that at least one pilar has a title
    let hasTitle = false;
    for (let i = 1; i <= 3; i++) {
        const titleField = document.getElementById(`pilar_titulo_${i}`);
        if (titleField && titleField.value.trim()) {
            hasTitle = true;
            break;
        }
    }

    if (!hasTitle) {
        // Show error on first title field
        const firstTitleField = document.getElementById('pilar_titulo_1');
        if (firstTitleField) {
            showFieldError(firstTitleField, 'Al menos uno de los pilares debe tener un título.');
        }
        isValid = false;
    }

    // Validate image files
    for (let i = 1; i <= 3; i++) {
        const imageInput = document.getElementById(`pilar_imagen_${i}`);
        if (imageInput && imageInput.files[0]) {
            if (!validateImageFile(imageInput, i)) {
                isValid = false;
            }
        }
    }

    return isValid;
}

/**
 * Submit form
 */
function submitForm() {
    const form = document.getElementById('pilares-form');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    // Show loading state
    form.classList.add('form-loading');
    submitBtn.disabled = true;
    
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin fa-fw me-1"></i> Guardando...';

    // Submit form
    form.submit();
}

/**
 * Show field error
 */
function showFieldError(field, message) {
    field.classList.add('is-invalid');
    
    let feedback = field.parentNode.querySelector('.invalid-feedback');
    if (feedback) {
        feedback.textContent = message;
        feedback.style.display = 'block';
    }
}

/**
 * Clear field error
 */
function clearFieldError() {
    this.classList.remove('is-invalid');
    
    const feedback = this.parentNode.querySelector('.invalid-feedback');
    if (feedback) {
        feedback.style.display = 'none';
    }
}

/**
 * Validate individual field
 */
function validateField() {
    const value = this.value.trim();
    
    if (this.hasAttribute('required') && !value) {
        showFieldError(this, 'Este campo es requerido.');
        return false;
    }
    
    clearFieldError.call(this);
    return true;
}

/**
 * Clear all form errors
 */
function clearAllErrors() {
    const form = document.getElementById('pilares-form');
    const invalidFields = form.querySelectorAll('.is-invalid');
    const feedbacks = form.querySelectorAll('.invalid-feedback');
    
    invalidFields.forEach(field => field.classList.remove('is-invalid'));
    feedbacks.forEach(feedback => feedback.style.display = 'none');
}

/**
 * Auto-dismiss alerts after 5 seconds
 */
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert.classList.contains('show')) {
                const closeBtn = alert.querySelector('.btn-close');
                if (closeBtn) {
                    closeBtn.click();
                }
            }
        }, 5000);
    });
});

/**
 * Character counter for textareas (optional enhancement)
 */
function addCharacterCounters() {
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        const maxLength = textarea.getAttribute('maxlength');
        if (maxLength) {
            const counter = document.createElement('small');
            counter.className = 'form-text text-muted character-counter';
            counter.textContent = `0/${maxLength} caracteres`;
            
            textarea.parentNode.appendChild(counter);
            
            textarea.addEventListener('input', function() {
                const currentLength = this.value.length;
                counter.textContent = `${currentLength}/${maxLength} caracteres`;
                
                if (currentLength > maxLength * 0.9) {
                    counter.classList.add('text-warning');
                } else {
                    counter.classList.remove('text-warning');
                }
            });
        }
    });
}

/**
 * Smooth scroll to first error
 */
function scrollToFirstError() {
    const firstError = document.querySelector('.is-invalid');
    if (firstError) {
        firstError.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });
        firstError.focus();
    }
}
