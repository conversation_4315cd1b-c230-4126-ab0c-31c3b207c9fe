/**
 * PROYECTOS ADMIN MODULE JAVASCRIPT
 * Handles CRUD operations for proyectos management
 */

$(document).ready(function() {
    // Initialize form handlers
    initializeFormHandlers();

    // Initialize modal handlers
    initializeModalHandlers();
});

/**
 * Initialize form submission handlers
 */
function initializeFormHandlers() {
    $('#formProyecto').on('submit', function(e) {
        e.preventDefault();
        
        // Clear previous validation states
        clearValidationStates();
        
        // Validate form
        if (!validateForm()) {
            return;
        }
        
        // Show loading state
        showLoadingState(true);
        
        // Prepare form data
        const formData = new FormData(this);
        
        // Submit form via AJAX
        $.ajax({
            url: 'listado-proyectos',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                handleFormSuccess(response);
            },
            error: function(xhr, status, error) {
                handleFormError(xhr, status, error);
            },
            complete: function() {
                showLoadingState(false);
            }
        });
    });
}

/**
 * Initialize modal event handlers
 */
function initializeModalHandlers() {
    // Reset form when modal is hidden
    $('#modalProyecto').on('hidden.bs.modal', function() {
        resetForm();
    });
    
    // Handle image preview
    $('#portada').on('change', function() {
        previewImage(this, 'portada-preview');
    });
    
    $('#logo_cliente').on('change', function() {
        previewImage(this, 'logo-preview');
    });
}

/**
 * Open modal for creating new proyecto
 */
function abrirModalCrear() {
    resetForm();
    $('#modalProyectoLabel').text('Crear Nuevo Proyecto');
    $('#action').val('crear');
    $('#btnSubmit').text('Crear');
    $('#portada').prop('required', true);
    $('#logo_cliente').prop('required', true);
    $('#currentPortadaPreview').hide();
    $('#currentLogoPreview').hide();
}

/**
 * Open modal for editing existing proyecto
 */
function abrirModalEditar(proyectoId) {
    resetForm();
    $('#modalProyectoLabel').text('Editar Proyecto');
    $('#action').val('editar');
    $('#id').val(proyectoId);
    $('#btnSubmit').text('Actualizar');
    $('#portada').prop('required', false);
    $('#logo_cliente').prop('required', false);
    
    $.ajax({
        url: 'listado-proyectos',
        type: 'POST',
        data: {
            action: 'obtener',
            id: proyectoId
        },
        dataType: 'json',
        success: function(response) {
            if (response.success && response.proyecto) {
                const proyecto = response.proyecto;
                
                // Populate form fields
                $('#titulo').val(proyecto.titulo);
                $('#ubicacion').val(proyecto.ubicacion);
                $('#descripcion').val(proyecto.descripcion);
                $('#prioridad').val(proyecto.prioridad);
                
                // Show current portada if exists
                if (proyecto.imagen_portada) {
                    $('#currentPortada').attr('src', './../resources/images/proyectos/' + proyecto.imagen_portada);
                    $('#currentPortadaPreview').show();
                }
                
                // Show current logo if exists
                if (proyecto.logo_cliente) {
                    $('#currentLogo').attr('src', './../resources/images/proyectos/logos/' + proyecto.logo_cliente);
                    $('#currentLogoPreview').show();
                }
                
                // Show modal
                $('#modalProyecto').modal('show');
            } else {
                showSweetAlertError('Error', response.message || 'No se pudo cargar los datos del proyecto.');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading proyecto:', error);
            showSweetAlertError('Error', 'Error al cargar los datos del proyecto.');
        }
    });
}

/**
 * Manage project images (placeholder for future implementation)
 */
function gestionarImagenes(proyectoId) {
    // TODO: Implement project images management
    // This could open a separate modal or redirect to a dedicated page
    showSweetAlertError('Función en desarrollo', 'La gestión de imágenes del proyecto estará disponible próximamente.');
}

/**
 * Delete proyecto with confirmation
 */
function eliminarProyecto(proyectoId) {
    Swal.fire({
        title: '¿Estás seguro?',
        text: 'Esta acción eliminará el proyecto permanentemente.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Sí, eliminar',
        cancelButtonText: 'Cancelar',
        background: '#2a2a2a',
        color: '#fff'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: 'listado-proyectos',
                type: 'POST',
                data: {
                    action: 'eliminar',
                    id: proyectoId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        showSweetAlertSuccess('Eliminado', response.message);
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        showSweetAlertError('Error', response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error deleting proyecto:', error);
                    showSweetAlertError('Error', 'Error al eliminar el proyecto.');
                }
            });
        }
    });
}

/**
 * Validate form before submission
 */
function validateForm() {
    let isValid = true;
    
    // Validate titulo
    const titulo = $('#titulo').val().trim();
    if (!titulo) {
        showFieldError('titulo', 'El título es requerido.');
        isValid = false;
    } else if (titulo.length < 2) {
        showFieldError('titulo', 'El título debe tener al menos 2 caracteres.');
        isValid = false;
    } else if (titulo.length > 200) {
        showFieldError('titulo', 'El título no puede exceder 200 caracteres.');
        isValid = false;
    }
    
    // Validate ubicacion
    const ubicacion = $('#ubicacion').val().trim();
    if (!ubicacion) {
        showFieldError('ubicacion', 'La ubicación es requerida.');
        isValid = false;
    } else if (ubicacion.length < 2) {
        showFieldError('ubicacion', 'La ubicación debe tener al menos 2 caracteres.');
        isValid = false;
    } else if (ubicacion.length > 200) {
        showFieldError('ubicacion', 'La ubicación no puede exceder 200 caracteres.');
        isValid = false;
    }
    
    // Validate descripcion
    const descripcion = $('#descripcion').val().trim();
    if (!descripcion) {
        showFieldError('descripcion', 'La descripción es requerida.');
        isValid = false;
    } else if (descripcion.length < 10) {
        showFieldError('descripcion', 'La descripción debe tener al menos 10 caracteres.');
        isValid = false;
    } else if (descripcion.length > 2000) {
        showFieldError('descripcion', 'La descripción no puede exceder 2000 caracteres.');
        isValid = false;
    }
    
    // Validate prioridad
    const prioridad = parseInt($('#prioridad').val());
    if (!prioridad || prioridad < 1) {
        showFieldError('prioridad', 'La prioridad es requerida y debe ser mayor a 0.');
        isValid = false;
    } else if (prioridad > 999) {
        showFieldError('prioridad', 'La prioridad no puede ser mayor a 999.');
        isValid = false;
    }
    
    // Validate files for creation
    const action = $('#action').val();
    if (action === 'crear') {
        const portadaFile = $('#portada')[0].files[0];
        const logoFile = $('#logo_cliente')[0].files[0];
        
        if (!portadaFile) {
            showFieldError('portada', 'La imagen de portada es requerida.');
            isValid = false;
        } else if (!validateImageFile(portadaFile, 2 * 1024 * 1024)) { // 2MB
            showFieldError('portada', 'Archivo de portada inválido. Máximo 2MB, formatos: JPG, PNG, WebP.');
            isValid = false;
        }
        
        if (!logoFile) {
            showFieldError('logo_cliente', 'El logo del cliente es requerido.');
            isValid = false;
        } else if (!validateImageFile(logoFile, 600 * 1024)) { // 600KB
            showFieldError('logo_cliente', 'Archivo de logo inválido. Máximo 600KB, formatos: JPG, PNG, WebP.');
            isValid = false;
        }
    } else if (action === 'editar') {
        // For editing, validate files only if they are selected
        const portadaFile = $('#portada')[0].files[0];
        const logoFile = $('#logo_cliente')[0].files[0];
        
        if (portadaFile && !validateImageFile(portadaFile, 2 * 1024 * 1024)) {
            showFieldError('portada', 'Archivo de portada inválido. Máximo 2MB, formatos: JPG, PNG, WebP.');
            isValid = false;
        }
        
        if (logoFile && !validateImageFile(logoFile, 600 * 1024)) {
            showFieldError('logo_cliente', 'Archivo de logo inválido. Máximo 600KB, formatos: JPG, PNG, WebP.');
            isValid = false;
        }
    }
    
    return isValid;
}

/**
 * Validate image file
 */
function validateImageFile(file, maxSize) {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    
    if (!allowedTypes.includes(file.type)) {
        return false;
    }
    
    if (file.size > maxSize) {
        return false;
    }
    
    return true;
}

/**
 * Show field validation error
 */
function showFieldError(fieldId, message) {
    const field = $('#' + fieldId);
    field.addClass('is-invalid');
    field.siblings('.invalid-feedback').text(message);
}

/**
 * Clear all validation states
 */
function clearValidationStates() {
    $('.form-control').removeClass('is-invalid');
    $('.invalid-feedback').text('');
}

/**
 * Reset form to initial state
 */
function resetForm() {
    $('#formProyecto')[0].reset();
    clearValidationStates();
    $('#currentPortadaPreview').hide();
    $('#currentLogoPreview').hide();
}

/**
 * Show loading state
 */
function showLoadingState(show) {
    if (show) {
        $('#loadingSpinner').removeClass('d-none');
        $('#btnSubmit').prop('disabled', true);
    } else {
        $('#loadingSpinner').addClass('d-none');
        $('#btnSubmit').prop('disabled', false);
    }
}

/**
 * Handle successful form submission
 */
function handleFormSuccess(response) {
    if (response.success) {
        showSweetAlertSuccess('Éxito', response.message);
        $('#modalProyecto').modal('hide');
        setTimeout(() => {
            location.reload();
        }, 1500);
    } else {
        showSweetAlertError('Error', response.message);
    }
}

/**
 * Handle form submission error
 */
function handleFormError(xhr, status, error) {
    console.error('Form submission error:', error);
    let message = 'Error al procesar la solicitud.';
    
    if (xhr.responseJSON && xhr.responseJSON.message) {
        message = xhr.responseJSON.message;
    }
    
    showSweetAlertError('Error', message);
}

/**
 * Preview uploaded image
 */
function previewImage(input, previewId) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            // You can add image preview functionality here if needed
            console.log('Image selected:', input.files[0].name);
        };
        reader.readAsDataURL(input.files[0]);
    }
}

/**
 * Show success alert
 */
function showSweetAlertSuccess(title, message) {
    Swal.fire({
        title: title,
        text: message,
        icon: 'success',
        confirmButtonColor: '#28a745',
        background: '#2a2a2a',
        color: '#fff'
    });
}

/**
 * Show error alert
 */
function showSweetAlertError(title, message) {
    Swal.fire({
        title: title,
        text: message,
        icon: 'error',
        confirmButtonColor: '#dc3545',
        background: '#2a2a2a',
        color: '#fff'
    });
}
